# Windows Administrator Junior Onboarding Guide

## Overview
Welcome to the Windows Administration team! This guide will help you get started as a junior administrator in our Active Directory environment managing 700+ servers (both VM and bare metal). This document outlines your initial responsibilities, learning path, and the tools you'll be working with.

## Your Role as a Junior Administrator

### Core Responsibilities
As a junior Windows administrator, you will be responsible for:

- **Group Policy Management**: Creating, modifying, and troubleshooting Group Policy Objects (GPOs)
- **Vulnerability Management**: Monitoring, assessing, and remediating security vulnerabilities
- **Server Provisioning**: Using SCCM to deploy and configure new servers
- **Ticketing System Management**: Creating and managing tickets for server build requests
- **SCCM Package Management**: Creating and deploying software packages
- **Continuous Learning**: Completing Udemy training courses to enhance your skills

---

## 1. Group Policy Objects (GPOs)

### What You'll Do
- Create and modify GPOs for user and computer configurations
- Link GPOs to appropriate Organizational Units (OUs)
- Troubleshoot GPO application issues
- Document GPO changes and their business justification

### Initial Tasks
- [ ] Review existing GPO structure and naming conventions
- [ ] Learn to use Group Policy Management Console (GPMC)
- [ ] Understand GPO inheritance and precedence
- [ ] Practice creating test GPOs in development environment

### Key Tools
- Group Policy Management Console (GPMC)
- Group Policy Results Wizard (gpresult)
- Group Policy Modeling Wizard

### Best Practices
- Always test GPOs in development environment first
- Use descriptive naming conventions
- Document all changes with business justification
- Regular backup of GPOs before modifications

---

## 2. Vulnerability Management

### What You'll Do
- Monitor vulnerability scanning reports
- Assess vulnerability severity and impact
- Coordinate with security team for remediation plans
- Track vulnerability remediation progress
- Generate vulnerability status reports

### Initial Tasks
- [ ] Access vulnerability management dashboard
- [ ] Learn vulnerability classification system (Critical, High, Medium, Low)
- [ ] Understand patch management process
- [ ] Review current vulnerability backlog

### Key Responsibilities
- Weekly vulnerability report reviews
- Escalate critical vulnerabilities immediately
- Maintain vulnerability tracking spreadsheets
- Coordinate with application owners for remediation

---

## 3. Server Provisioning with SCCM

### What You'll Do
- Deploy operating systems to new servers
- Configure server roles and features
- Apply security baselines and configurations
- Validate server deployments

### Initial Tasks
- [ ] Learn SCCM console navigation
- [ ] Understand task sequence creation and modification
- [ ] Review standard server build procedures
- [ ] Practice server deployments in lab environment

### SCCM Components You'll Work With
- **Operating System Deployment (OSD)**
- **Task Sequences**
- **Boot Images**
- **Driver Packages**
- **Configuration Baselines**

### Server Build Process
1. Receive approved server build ticket
2. Verify hardware specifications
3. Select appropriate task sequence
4. Deploy OS and base configuration
5. Apply security baselines
6. Validate deployment
7. Update ticket with completion status

---

## 4. Ticketing System Management

### What You'll Do
- Create detailed server build request tickets
- Track ticket progress and status updates
- Communicate with requestors about requirements
- Escalate complex requests to senior administrators

### Ticket Creation Best Practices
- Include all required server specifications
- Specify business justification
- Set appropriate priority levels
- Add relevant stakeholders to notifications
- Attach any supporting documentation

### Required Information for Server Build Tickets
- Server purpose and business justification
- Hardware specifications (CPU, RAM, Storage)
- Operating system version and edition
- Network requirements (IP, VLAN, firewall rules)
- Software requirements
- Security requirements
- Timeline and dependencies

---

## 5. SCCM Package Management

### What You'll Do
- Create software deployment packages
- Test package installations
- Deploy packages to target collections
- Monitor deployment success rates
- Troubleshoot failed deployments

### Package Creation Process
1. Obtain software installation files
2. Create application in SCCM console
3. Configure detection methods
4. Set up deployment types
5. Test in pilot collection
6. Deploy to production collections
7. Monitor and report on deployment status

### Package Types You'll Work With
- **Applications**: Modern software deployment method
- **Packages**: Legacy deployment method for scripts and utilities
- **Software Updates**: OS and application patches
- **Configuration Items**: Settings and compliance rules

---

## 6. Training and Professional Development

### Required Udemy Courses
Complete these courses within your first 90 days:

#### Month 1
- [ ] "Windows Server 2019/2022 Administration"
- [ ] "Active Directory Fundamentals"
- [ ] "Group Policy Management"

#### Month 2
- [ ] "SCCM 2019/2022 Complete Guide"
- [ ] "PowerShell for System Administrators"
- [ ] "Windows Security Fundamentals"

#### Month 3
- [ ] "Vulnerability Management Best Practices"
- [ ] "IT Service Management (ITSM) Fundamentals"
- [ ] "Network Fundamentals for System Administrators"

### Learning Resources
- Internal knowledge base and documentation
- Microsoft Learn platform
- TechNet documentation
- Internal training videos and guides

---

## Environment Overview

### Infrastructure Scale
- **700+ Servers**: Mix of virtual machines and bare metal
- **Active Directory Domain**: Multi-site domain environment
- **Virtualization**: VMware vSphere environment
- **Configuration Management**: Microsoft SCCM
- **Monitoring**: Various monitoring solutions

### Key Systems and Tools
- **Active Directory Domain Services (AD DS)**
- **System Center Configuration Manager (SCCM)**
- **Group Policy Management Console (GPMC)**
- **PowerShell ISE/VS Code**
- **Vulnerability Management Platform**
- **IT Service Management (ITSM) Platform**

---

## First Week Checklist

### Day 1-2: Account Setup and Access
- [ ] Receive domain administrator account
- [ ] Access SCCM console
- [ ] Access vulnerability management dashboard
- [ ] Access ticketing system
- [ ] Join relevant security groups
- [ ] Set up Udemy learning account

### Day 3-5: Environment Familiarization
- [ ] Tour of server room/data center
- [ ] Review organizational chart and team structure
- [ ] Shadow senior administrator
- [ ] Review current projects and priorities
- [ ] Begin first Udemy course

---

## Success Metrics

### 30-Day Goals
- Complete initial training courses
- Successfully create first GPO (with supervision)
- Complete first server build using SCCM
- Create and manage first set of tickets

### 60-Day Goals
- Independently manage vulnerability reports
- Create and deploy first SCCM package
- Demonstrate proficiency with PowerShell basics
- Complete intermediate training courses

### 90-Day Goals
- Handle routine administrative tasks independently
- Mentor newer team members
- Contribute to process improvement initiatives
- Complete advanced training courses

---

## Escalation Procedures

### When to Escalate
- Critical vulnerabilities (CVSS 9.0+)
- Server build failures affecting business operations
- GPO issues causing widespread user impact
- Any security incidents or suspicious activity

### Escalation Contacts
- **Immediate Supervisor**: [Name and contact]
- **Senior Windows Administrator**: [Name and contact]
- **Security Team**: [Contact information]
- **Network Team**: [Contact information]
- **After-hours Support**: [Contact information]

---

## Additional Resources

### Documentation
- Internal Windows Administration Wiki
- Standard Operating Procedures (SOPs)
- Architecture diagrams and network maps
- Emergency procedures and contact lists

### Communities and Support
- Internal Windows Admin Teams channel
- Microsoft TechNet forums
- PowerShell community forums
- SCCM community resources

---

*This document will be updated regularly. Please check for the latest version and provide feedback for improvements.*

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Next Review**: [Date + 3 months]
